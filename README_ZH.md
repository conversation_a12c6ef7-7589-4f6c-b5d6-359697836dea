# Doc2Dev

Doc2Dev 可以为 LLM 和 AI 编程助手提供实时的文档查询。Doc2Dev 可对任意 GitHub 仓库进行文档索引与查询，并通过 MCP 轻松集成至 Cursor、Windsurf 等 AI 编程工具。

![](https://chengzw258.oss-cn-beijing.aliyuncs.com/Article/202508072302799.png)

## 开发者面临的痛点

作为开发者，你是否经常遇到这样的困扰？

- AI 编程助手常常编造根本不存在的 API 接口，导致严重的代码幻觉问题。
- 主流大语言模型的训练数据滞后于技术更新，生成的代码常常基于已经废弃的旧版 API。
- 尽管 AI 可以快速生成代码，但调试和排错却耗费了大量时间，反而拖慢了开发进度。

Doc2Dev 的出现正是为了解决这些问题。它利用 OceanBase 向量数据库和大型语言模型(LLM)的强大能力，从官方源头获取最新的、版本特定的文档和相关代码示例，将这些信息注入到 LLM 的上下文中，从而有效提高 LLM 生成代码的质量。

## 核心优势

- **最新、最准确的代码**：获取反映最新库版本和最佳实践的建议。
- **减少调试时间**：减少因过时的 AI 知识导致的错误修复时间。
- **拒绝代码幻觉**：依赖于已记录的、存在的函数和 API。
- **精准版本**：能根据特定库版本给出准确答案。
- **无缝工作流程**：直接集成到现有的 AI 编程助手中，无需频繁切换到文档网站。
- **语义理解**：基于向量嵌入的搜索超越了传统的关键词匹配，能够理解查询的语义内容。

## 设置环境变量

请根据 `.env.example` 文件的说明，创建 `.env` 文件并配置相应的环境变量。

## 启动服务

1. 启动后端服务：

```bash
cd backend
uv run main.py
```

2. 启动前端服务：
   
```bash
cd frontend
npm run dev
```
